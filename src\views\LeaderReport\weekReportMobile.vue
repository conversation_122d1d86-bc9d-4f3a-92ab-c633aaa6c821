<template>
  <div id="dayReport">
    <div class="targetMange_Box">
      <p class="time"
        ><img style="margin-right: 5px" src="../../assets/svg/time.svg" />{{ currentTime1 }}</p
      >
      <div class="box-item">
        <img class="icon" src="../../assets/svg/all.svg" />
        <div class="chart-slide">
          <div class="chart-container">
            <Chart :data="chartData" width="100%" height="100%" />
          </div>
        </div>
        <div class="table-slide">
          <div class="table-container">
            <a-table
              :columns="totalColumns"
              :data-source="totalData"
              :pagination="false"
              rowKey="id"
              :border="true"
              :scroll="{ x: 800, y: 300 }"
              size="small"
              tableLayout="auto"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'rate'">
                  <Icon
                    v-if="Number(record.yesterdayAverage) < Number(record.todayAverage)"
                    :icon="'toward-up|svg'"
                  />
                  <Icon
                    v-if="Number(record.yesterdayAverage) > Number(record.todayAverage)"
                    :icon="'toward-down|svg'"
                  />
                  <Icon
                    v-if="Number(record.yesterdayAverage) === Number(record.todayAverage)"
                    :icon="'toward-ping|svg'"
                  />
                </template>
              </template>
            </a-table>
          </div>
        </div>
      </div>
      <!-- 代表排名表 -->
      <div class="box-item">
        <img class="icon" src="../../assets/svg/daibiao.svg" />
        <div class="table-container">
          <a-table
            :columns="rankColumns"
            :data-source="rankData"
            :pagination="false"
            rowKey="id"
            :border="true"
            :scroll="{ x: 'max-content', y: 300 }"
            size="small"
            tableLayout="auto"
          />
        </div>
      </div>
      <div class="box-item">
        <img class="icon" src="../../assets/svg/wenti.svg" />
        <div class="table-container">
          <a-table
            :columns="questionColumns"
            :data-source="questionData"
            :pagination="false"
            rowKey="id"
            :border="true"
            :scroll="{ x: 1000, y: 300 }"
            size="small"
            tableLayout="auto"
            :show-expand-column="false"
            :expandedRowKeys="Array.from(expandedReasonRows)"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'taskKpiList'">
                <span
                  style="display: block; word-break: break-all; word-wrap: break-word"
                  v-for="(item, index) in record.taskKpiList"
                  :key="index"
                >
                  {{ item.kpiName }}({{ item.real }}/{{ item.baseLine }})<span
                    v-if="index !== record.taskKpiList.length - 1"
                    >,</span
                  >
                </span>
              </template>
              <template v-if="column.key === 'reason'">
                <div>
                  <a-button type="link" @click="toggleReasonExpand(record.id)">
                    {{ expandedReasonRows.has(record.id) ? '收起' : '查看' }}
                  </a-button>
                </div>
              </template>
            </template>
            <template #expandedRowRender="{ record }">
              <div style="padding: 16px; background-color: #fafafa">
                <a-table
                  :columns="reasonColumns"
                  :data-source="[
                    {
                      id: 'reason_row',
                      reasonText: getReasonText(record.taskKpiList) || '暂无原因分析',
                    },
                  ]"
                  :pagination="false"
                  :show-header="false"
                  size="small"
                  :bordered="false"
                >
                  <template #bodyCell="{ column: reasonColumn, record: reasonRecord }">
                    <template v-if="reasonColumn.key === 'reasonText'">
                      <div
                        style="
                          word-break: break-all;
                          word-wrap: break-word;
                          line-height: 1.5;
                          padding: 8px;
                          background-color: #fff;
                          border-radius: 4px;
                          border: 1px solid #d9d9d9;
                        "
                      >
                        {{ reasonRecord.reasonText }}
                      </div>
                    </template>
                  </template>
                </a-table>
              </div>
            </template>
          </a-table>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import Chart from '../performanceReport/components/chart.vue';
  import {
    getPerformanceRadarData,
    getPerformanceBottomRanking,
    getPerformanceKpiTrend,
    getPerformanceRanking,
  } from '/@/api/LeaderReport/index';
  import { useRoute } from 'vue-router';
  import { Icon } from '/@/components/Icon';
  import { getCurrentWeekDays } from './utils';

  const route = useRoute();
  const currentTime1 = ref('');
  const chartData = ref<any>({
    data: [],
    indicator: [],
  });

  // 计算行合并的函数
  const calculateRowSpan = (data: any[], index: number, field: string) => {
    if (index === 0) {
      // 第一行，计算连续相同值的数量
      let count = 1;
      const currentValue = data[index][field];
      for (let i = index + 1; i < data.length; i++) {
        if (data[i][field] === currentValue) {
          count++;
        } else {
          break;
        }
      }
      return count;
    } else {
      // 非第一行，检查是否与前一行相同
      if (data[index][field] === data[index - 1][field]) {
        return 0; // 合并到前一行
      } else {
        // 计算从当前行开始连续相同值的数量
        let count = 1;
        const currentValue = data[index][field];
        for (let i = index + 1; i < data.length; i++) {
          if (data[i][field] === currentValue) {
            count++;
          } else {
            break;
          }
        }
        return count;
      }
    }
  };

  const totalColumns = ref([
    {
      title: '目标类型',
      dataIndex: 'taskName',
      key: 'taskName',
      align: 'center',
      width: 100,
      minWidth: 100,
      customRender: ({ text, index }: any) => {
        const rowSpan = calculateRowSpan(totalData.value, index, 'taskName');
        return {
          children: text,
          props: {
            rowSpan: rowSpan,
          },
        };
      },
    },
    {
      title: 'KPI名称',
      dataIndex: 'kpiName',
      key: 'kpiName',
      align: 'center',
      width: 100,
      minWidth: 100,
    },
    {
      title: '目标值',
      dataIndex: 'real',
      key: 'real',
      align: 'center',
      width: 80,
      minWidth: 80,
    },
    {
      title: '上周',
      dataIndex: 'yesterdayAverage',
      key: 'yesterdayAverage',
      align: 'center',
      width: 80,
      minWidth: 80,
    },
    {
      title: '本周',
      dataIndex: 'todayAverage',
      key: 'todayAverage',
      align: 'center',
      width: 80,
      minWidth: 80,
    },
    {
      title: '趋势',
      dataIndex: 'rate',
      key: 'rate',
      align: 'center',
      width: 80,
      minWidth: 80,
    },
  ]);
  const totalData = ref<any[]>([]);
  const rankColumns = ref<any[]>([]);
  // 生成动态列配置
  const generateRankColumns = (data: any[]) => {
    // 固定列
    const fixedColumns = [
      {
        title: '员工姓名',
        dataIndex: 'saleManName',
        key: 'saleManName',
        align: 'center',
        width: 100,
        minWidth: 100,
      },
      {
        title: 'EDP工号',
        dataIndex: 'edpCode',
        key: 'edpCode',
        align: 'center',
        width: 100,
        minWidth: 100,
      },
      {
        title: '所属辖区',
        dataIndex: 'departNames',
        key: 'departNames',
        align: 'center',
        width: 200,
        minWidth: 200,
      },
      {
        title: '绩效总分（日）',
        dataIndex: 'totalScore',
        key: 'totalScore',
        align: 'center',
        width: 120,
        minWidth: 120,
      },
      {
        title: '全国排名',
        dataIndex: 'nationalRank',
        key: 'nationalRank',
        align: 'center',
        width: 100,
        minWidth: 100,
      },
      {
        title: '省区排名',
        dataIndex: 'provincialRank',
        key: 'provincialRank',
        align: 'center',
        width: 100,
        minWidth: 100,
      },
    ];

    // 动态列 - 根据taskKpiList生成
    const dynamicColumns: any[] = [];

    if (data && data.length > 0 && data[0].taskKpiList) {
      // 遍历taskKpiList，每个task创建一个合并列
      data[0].taskKpiList.forEach((task: any) => {
        const { taskName, kpiList } = task;

        if (kpiList && kpiList.length > 0) {
          // 为每个task的kpiList创建子列
          const children: any[] = [];
          kpiList.forEach((kpi: any) => {
            children.push({
              title: kpi.kpiName, // 小表头
              dataIndex: `kpi_${taskName}_${kpi.kpiName}`,
              key: `kpi_${taskName}_${kpi.kpiName}`,
              align: 'center',
              width: 120,
              minWidth: 120,
              // isSlot: true,
              customRender: ({ record }: any) => {
                // 在record的taskKpiList中找到对应的task，然后在其kpiList中找到对应的kpi
                const taskData = record.taskKpiList?.find(
                  (item: any) => item.taskName === taskName,
                );
                if (taskData && taskData.kpiList) {
                  const kpiData = taskData.kpiList.find(
                    (item: any) => item.kpiName === kpi.kpiName,
                  );
                  return kpiData ? kpiData.real : '-'; // 绑定real字段
                }
                return '-';
              },
            });
            // 权重列
            // children.push({
            //   title: `权重`, // 权重列标题
            //   dataIndex: `kpi_${taskName}_${kpi.kpiName}_weight`,
            //   key: `kpi_${taskName}_${kpi.kpiName}_weight`,
            //   align: 'center',
            //   width: 120,
            //   minWidth: 120,
            //   // isSlot: true,
            //   customRender: ({ record }: any) => {
            //     const taskData = record.taskKpiList?.find(
            //       (item: any) => item.taskName === taskName,
            //     );
            //     if (taskData && taskData.kpiList) {
            //       const kpiData = taskData.kpiList.find(
            //         (item: any) => item.kpiName === kpi.kpiName,
            //       );
            //       return kpiData ? `${kpiData.weight || 0}%` : '-'; // 显示权重百分比
            //     }
            //     return '-';
            //   },
            // });

            // 分数列
            children.push({
              title: `分数`, // 分数列标题
              dataIndex: `kpi_${taskName}_${kpi.kpiName}_score`,
              key: `kpi_${taskName}_${kpi.kpiName}_score`,
              align: 'center',
              width: 80,
              minWidth: 80,
              // isSlot: true,
              customRender: ({ record }: any) => {
                const taskData = record.taskKpiList?.find(
                  (item: any) => item.taskName === taskName,
                );
                if (taskData && taskData.kpiList) {
                  const kpiData = taskData.kpiList.find(
                    (item: any) => item.kpiName === kpi.kpiName,
                  );
                  return kpiData ? kpiData.score || 0 : '-'; // 显示分数或实际值
                }
                return '-';
              },
            });
          });

          // 创建父列（合并列）- taskName作为大表头
          dynamicColumns.push({
            title: taskName, // 大表头
            key: taskName,
            align: 'center',
            children: children,
          });
        }
      });
    }

    return [...fixedColumns, ...dynamicColumns];
  };
  const rankData = ref<any[]>([]);

  const questionColumns = ref([
    {
      title: '员工姓名',
      dataIndex: 'saleManName',
      key: 'saleManName',
      align: 'center',
      width: 100,
      minWidth: 100,
    },
    {
      title: 'EDP工号',
      dataIndex: 'edpCode',
      key: 'edpCode',
      align: 'center',
      width: 100,
      minWidth: 100,
    },
    {
      title: '所属辖区',
      dataIndex: 'departNames',
      key: 'departNames',
      align: 'center',
      width: 180,
      minWidth: 180,
    },
    {
      title: '绩效总分（日）',
      dataIndex: 'totalScore',
      key: 'totalScore',
      align: 'center',
      width: 120,
      minWidth: 120,
    },
    {
      title: '全国排名',
      dataIndex: 'nationalRank',
      key: 'nationalRank',
      align: 'center',
      width: 100,
      minWidth: 100,
    },
    {
      title: '省区排名',
      dataIndex: 'provincialRank',
      key: 'provincialRank',
      align: 'center',
      width: 100,
      minWidth: 100,
    },
    {
      title: '未达标项',
      dataIndex: 'taskKpiList',
      key: 'taskKpiList',
      align: 'center',
      width: 100,
      minWidth: 100,
    },
    {
      title: '原因分析',
      dataIndex: 'reason',
      key: 'reason',
      align: 'center',
      width: 200,
      minWidth: 200,
    },
  ]);

  const questionData = ref<any[]>([]);
  // 嵌套表格的列配置
  const reasonColumns = ref([
    {
      title: '原因分析',
      dataIndex: 'reasonText',
      key: 'reasonText',
      align: 'left',
    },
  ]);

  // 控制reason展开状态
  const expandedReasonRows = ref<Set<string>>(new Set());

  // 切换reason展开状态
  const toggleReasonExpand = (recordId: string) => {
    const newSet = new Set(expandedReasonRows.value);
    if (newSet.has(recordId)) {
      newSet.delete(recordId);
    } else {
      newSet.add(recordId);
    }
    expandedReasonRows.value = newSet;
  };

  // 获取reason字段拼接字符串
  const getReasonText = (taskKpiList: any[]) => {
    if (!taskKpiList || taskKpiList.length === 0) return '';
    return taskKpiList
      .filter((reason) => reason.reason)
      .map((item) => {
        return item.kpiName + '：' + item.reason;
      })
      .join('');
  };
  const intData = async (deptId: any, frequency: any, currentTime: any) => {
    try {
      const tmp = {
        deptId,
        currentTime,
        frequency,
      };

      const res = await Promise.all([
        getPerformanceRadarData(tmp), // 雷达图数据
        getPerformanceRanking(tmp), // 排名数据
        getPerformanceBottomRanking(tmp), // 倒数排名数据
        getPerformanceKpiTrend(tmp), // KPI趋势数据
      ]);
      console.log(res);

      // 处理雷达图数据
      const arrAll = [];
      if (res[0] && res[0].length >= 2) {
        arrAll.push(...res[0][0].taskKpiList.map((item: any) => item.taskName));
        arrAll.push(...res[0][1].taskKpiList.map((item: any) => item.taskName));
        res[0] = [
          {
            taskKpiList: [...new Set(arrAll)].map((item: any) => {
              let i = res[0][0].taskKpiList.findIndex((it: any) => it.taskName === item);
              if (i >= 0) {
                return {
                  ...res[0][0].taskKpiList[i],
                };
              } else {
                return {
                  taskName: item,
                  score: 0,
                  weight: 0,
                };
              }
            }),
          },
          {
            taskKpiList: [...new Set(arrAll)].map((item: any) => {
              let i = res[0][1].taskKpiList.findIndex((it: any) => it.taskName === item);
              if (i >= 0) {
                return {
                  ...res[0][1].taskKpiList[i],
                };
              } else {
                return {
                  taskName: item,
                  score: 0,
                  weight: 0,
                };
              }
            }),
          },
        ];
      } else {
        arrAll.push(...res[0][0].taskKpiList.map((item: any) => item.taskName));
        res[0] = [
          {
            taskKpiList: [...new Set(arrAll)].map((item: any) => {
              return {
                taskName: item,
                score: 0,
                weight: 0,
              };
            }),
          },
          {
            taskKpiList: [...new Set(arrAll)].map((item: any) => {
              let i = res[0][0].taskKpiList.findIndex((it: any) => it.taskName === item);
              if (i >= 0) {
                return {
                  ...res[0][0].taskKpiList[i],
                };
              } else {
                return {
                  taskName: item,
                  score: 0,
                  weight: 0,
                };
              }
            }),
          },
        ];
      }
      // 处理雷达图数据
      if (res[0] && res[0].length >= 2) {
        chartData.value.indicator = res[0][1].taskKpiList.map((item: any) => {
          return {
            text: item.taskName,
            max: item.weight,
          };
        });

        const data = [
          {
            value: res[0][0].taskKpiList.map((item: any) => {
              return item.score;
            }),
            name: '上周',
          },
          {
            value: res[0][1].taskKpiList.map((item: any) => {
              return item.score;
            }),
            name: '本周',
          },
        ];
        chartData.value.data = data as any;
      }

      // 处理排名数据
      if (res[1] && res[1].length > 0) {
        const processedRankData = res[1].map((item: any, index: number) => ({
          ...item,
          id: item.edpCode || `rank_${index}`,
        }));

        rankData.value = processedRankData;
        rankColumns.value = generateRankColumns(processedRankData);
      }

      // 处理KPI趋势数据用于总览表格
      if (res[3] && res[3].length > 0) {
        const processedTotalData = res[3].map((item: any, index: number) => ({
          ...item,
          id: item.id || `total_${index}`,
        }));
        totalData.value = processedTotalData;
      }

      // 处理倒数排名数据用于问题表格
      if (res[2] && res[2].length > 0) {
        const processedQuestionData = res[2].map((item: any, index: number) => ({
          ...item,
          id: item.edpCode || `question_${index}`,
        }));
        questionData.value = processedQuestionData;
      } else {
        questionData.value = [];
      }
    } catch (error) {
      console.error('获取数据失败:', error);
      // 如果API调用失败，设置空数据
      chartData.value = { data: [], indicator: [] };
      rankData.value = [];
      totalData.value = [];
      questionData.value = [];
    }
  };
  onMounted(() => {
    // currentTime.value = dayjs().format('YYYY-MM-DD');
    const { deptId, currentTime } = route.query;
    const weekDays = getCurrentWeekDays(currentTime, '.');
    currentTime1.value = weekDays[0] + '-' + weekDays[6];

    // frequency 1日2月3周
    const deptIdC = deptId;
    intData(deptIdC, '3', currentTime);
  });
</script>

<style scoped lang="less">
  ::v-deep {
    .ant-table.ant-table-small .ant-table-tbody .ant-table-wrapper:only-child .ant-table {
      margin: 0px !important;
    }
  }
  #dayReport {
    width: 100%;
    min-height: 100vh;
    .targetMange_Box {
      width: 100%;
      height: 100%;
      .title {
        font-size: 20px;
        text-align: center;
        margin-bottom: 20px;
        margin-right: 10px;
      }
      .time {
        width: 200px;
        height: 28px;
        color: #ff9201;

        /* 点文本-加粗/14pt bold */
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 24px;
        background: rgba(255, 255, 255, 0.25);
      }
      .subtitle {
        font-size: 16px;
        display: flex;
        background: #ffedd0;
        height: 30px;
        line-height: 30px;
        font-weight: bold;
        &::before {
          content: '';
          width: 4px;
          height: 100%;
          background: #f9c048;
          display: inline-block;
          margin-right: 10px;
        }
      }
      .chart-slide,
      .table-slide {
        height: 100%;
        width: 100%;
        padding: 0 10px;
      }

      .chart-container {
        height: 280px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .table-container {
        height: 100%;
        width: 100%;
        overflow: hidden;

        :deep(.ant-table-wrapper) {
          height: 100%;
        }

        :deep(.ant-table) {
          height: 100%;
          table-layout: auto; // 允许表格自动调整列宽
        }

        :deep(.ant-table-container) {
          height: 100%;
        }

        :deep(.ant-table-header) {
          overflow: hidden; // 表头不滚动
        }

        :deep(.ant-table-body) {
          height: calc(100% - 55px); // 减去表头高度
          overflow: auto;
        }

        :deep(.ant-table-thead > tr > th) {
          position: sticky;
          top: 0;
          z-index: 1;
          background: #fafafa;
          white-space: nowrap;
          padding: 8px 4px;
          font-size: 12px;
          min-width: 80px; // 设置最小宽度防止挤压
          text-overflow: ellipsis;
          border-right: 1px solid #f0f0f0;
        }

        :deep(.ant-table-tbody > tr > td) {
          white-space: nowrap;
          padding: 8px 4px;
          font-size: 12px;
          min-width: 80px; // 设置最小宽度防止挤压
          text-overflow: ellipsis;
          border-right: 1px solid #f0f0f0;
        }
      }
      .box-item {
        box-sizing: border-box;
        width: 100%;
        margin-top: 20px;
        overflow: hidden;
        border-radius: 24px;
        background: #fff;

        /* 下层投影 */
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.12);
        padding: 40px 10px 10px 10px;
        position: relative;
        .icon {
          position: absolute;
          top: 0;
          left: 0;
          z-index: 100;
        }
      }
    }
  }

  /* 媒体查询 - 宽度小于等于540px */
  @media (max-width: 540px) {
    #dayReport {
      padding: 8px;
      background: url('../../assets/svg/week-bg.svg') no-repeat;
      background-size: contain;

      .targetMange_Box {
        padding: 10px;
        .time {
          margin-top: 15vh;
        }
      }
    }
  }

  /* 媒体查询 - 宽度大于540px */
  @media (min-width: 541px) {
    #dayReport {
      background: url('../../assets/svg/week_bg.svg') no-repeat;
      background-size: contain;
      padding: 8px;

      .targetMange_Box {
        padding: 10px;

        .time {
          margin: 9% auto 0;
        }
      }
    }
  }
</style>
